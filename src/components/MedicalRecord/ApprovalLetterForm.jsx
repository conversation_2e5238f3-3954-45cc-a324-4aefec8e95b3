import { But<PERSON>, <PERSON>, Switch, Row, Col } from 'antd'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useQueryClient } from '@tanstack/react-query'
import { useForm } from 'antd/es/form/Form'
import useDeepCompareEffect from 'use-deep-compare-effect'
import { useMedicalRecord, MEDICAL_RECORD_KEYS } from '../../queryHooks/useMedicalRecord'
import { displayDate, handleError } from '../../common/helpers'
import { updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import { FORM_MODE } from '../../common/constant'
import MedicalRecordField, { MEDICAL_RECORD_FIELD_TYPE } from './MedicalRecordField'
import useApp from 'antd/es/app/useApp'
import DocumentStore from '../../common/components/DocumentStore/DocumentStore'
import PdfViewer from '../../common/components/PdfViewer'
import dayjs from '../../common/dayjs'
import { usePatientVisit } from '../Visit/hooks/usePatientVisit'
import fvLogoWithText from '../../assets/logoFV-withtext.png'
import { MODULE_AUTH } from '../../store/auth'
import { handlePrintPDF } from '../../SI/helper'
import { approvalLetterContent } from './MedicalRecordConstant'
import AsyncButton from '../../common/components/AsyncButton'
import { usePatient } from '../../queryHooks/usePatient'
import { usePopulatePDF } from '../../common/hooks/usePopulatePDF'
import config from '../../common/config'

const ApprovalLetterForm = ({ mainVisit, selectedMedicalRecordId, patientId }) => {
  const onlyUsePopulatePDF = true // disable raw html form

  const [form] = useForm()
  const app = useApp()
  const queryClient = useQueryClient()
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  // hooks
  const { medicalRecordDetail, medicalCondition } = useMedicalRecord({
    medicalRecordFormId: selectedMedicalRecordId,
  })
  const {
    data: { healthInsuranceCards },
  } = usePatientVisit(mainVisit?.patient_visit_id)
  const firstCard = healthInsuranceCards?.[0]
  const { patient } = usePatient({ patientId: patientId || mainVisit?.patient_id })

  const [formMode, setFormMode] = useState(FORM_MODE.view)
  const [openDocumentStore, setOpenDocumentStore] = useState(false)
  const [attachments, setAttachments] = useState([])
  const [viewFileOnly, setViewFileOnly] = useState()
  const [pdfTemplateLocal, setPdfTemplateLocal] = useState(null)
  const [pdfTemplateEN, setPdfTemplateEN] = useState(null)

  // PDF hook
  const { populateAndExportPDF: populateAndExportPDFLocal } = usePopulatePDF(pdfTemplateLocal)
  const { populateAndExportPDF: populateAndExportPDFEN } = usePopulatePDF(pdfTemplateEN)

  const isEdit = formMode === FORM_MODE.edit

  const fullName = patient?.Fullname || ''
  const sex = patient?.Sex === 'Male' ? 'Nam' : 'Nữ'
  const nationId = patient?.Nation_id || ''
  const dob = displayDate(patient?.DOB, 'DD/MM/YYYY')
  const HN = patient?.HN || ''

  // default values
  useDeepCompareEffect(() => {
    form.setFieldsValue({
      signedDateTime: medicalRecordDetail?.signed_date_time
        ? dayjs(medicalRecordDetail.signed_date_time)
        : dayjs(),
    })
  }, [medicalRecordDetail, medicalCondition, form])

  // auto show attachment
  useEffect(() => {
    setViewFileOnly(!!attachments[0] && !isEdit)
  }, [attachments, isEdit])

  const handleSaveMedicalRecordForm = async () => {
    if (!selectedMedicalRecordId) {
      return
    }

    const values = form.getFieldsValue()

    try {
      const newRecord = {
        lu_user_id: currentUser?.User_id,
        signed_date_time: values.signedDateTime,
      }

      await updateListItemService(lists.medical_record_form, selectedMedicalRecordId, newRecord)

      // Invalidate the query cache to force a refresh of the medical record data
      queryClient.invalidateQueries({
        queryKey: [MEDICAL_RECORD_KEYS.MEDICAL_RECORD, selectedMedicalRecordId],
      })

      app.message.success('Lưu thành công')
    } catch (error) {
      handleError(error, 'handleSaveMedicalRecordForm')
    }
  }

  // fetch template of the form
  useEffect(() => {
    fetchPdfTemplate('L')
    fetchPdfTemplate('EN')
  }, [])

  // Fetch PDF template
  const fetchPdfTemplate = async (language = 'L') => {
    try {
      const templatePath =
        language === 'L'
          ? '/template/073 CNS - Consent to Social Insurance Coverage Limitations at FV Hospital - VN.pdf'
          : '/template/073 CNS - Consent to Social Insurance Coverage Limitations at FV Hospital - Eng.pdf'
      const response = await fetch(config.HOME_PAGE + templatePath)
      if (!response.ok) {
        throw new Error('Failed to fetch PDF template')
      }
      const blob = await response.blob()

      language === 'L' ? setPdfTemplateLocal(blob) : setPdfTemplateEN(blob)

      return blob
    } catch (error) {
      handleError(error, 'fetchPdfTemplate')
      app.message.error('Không thể tải template PDF')
      throw error
    }
  }

  // Handle PDF download
  const handleDownloadPDF = async (language = 'L') => {
    try {
      // Fetch template if not already loaded
      let template = language === 'L' ? pdfTemplateLocal : pdfTemplateEN
      if (!template) {
        template = await fetchPdfTemplate(language)
        return
      }

      // Map form data to PDF fields
      const pdfData = {
        // Patient information fields - these field names should match the PDF form fields
        fullName: fullName,
        sex: language === 'L' ? sex : patient?.Sex || '',
        dob: dob,
        hn: HN,
        address: firstCard?.card_address || '',
        cccd: nationId,
        phone: patient?.Phone || '',
        card: firstCard?.card_code || '',
        cardDate: firstCard?.effective_date
          ? dayjs(firstCard?.effective_date).format('DD/MM/YYYY')
          : '',
      }

      const fileName = `GiayChapThuan_${medicalRecordDetail?.title || HN || 'form'}_${dayjs().format('DDMMYYYY')}.pdf`

      if (language === 'L') {
        await populateAndExportPDFLocal(pdfData, fileName)
      } else {
        await populateAndExportPDFEN(pdfData, fileName)
      }
      app.message.success('Tải xuống PDF thành công')
    } catch (error) {
      handleError(error, 'handleDownloadPDF')
      app.message.error('Không thể tải xuống PDF')
    }
  }

  return (
    <div>
      <div
        className="sticky-top d-flex justify-content-between align-items-center gap-2"
        style={{ top: 105 }}>
        <div></div>
        <div className="d-flex align-items-center gap-2">
          {/* toggle to view file or form */}
          <div className="d-flex align-items-center me-2 gap-2">
            <Switch onChange={() => setViewFileOnly(!viewFileOnly)} checked={viewFileOnly} /> Chỉ
            xem File đính kèm
          </div>
          <Button
            hidden={onlyUsePopulatePDF}
            variant={isEdit ? 'outlined' : 'solid'}
            color={'blue'}
            icon={<i className="fa fa-edit" />}
            onClick={() => setFormMode(isEdit ? FORM_MODE.view : FORM_MODE.edit)}>
            {isEdit ? 'Tắt Chỉnh sửa' : 'Chỉnh sửa'}
          </Button>
          <AsyncButton
            icon={<i className="fa fa-save" />}
            hidden={!isEdit || onlyUsePopulatePDF}
            onClick={handleSaveMedicalRecordForm}>
            Lưu
          </AsyncButton>
          <Button
            variant="solid"
            color="green"
            icon={
              openDocumentStore ? <i className="fa fa-close" /> : <i className="fa fa-upload" />
            }
            onClick={() => setOpenDocumentStore(!openDocumentStore)}>
            {openDocumentStore ? 'Đóng' : 'Mở'} upload
          </Button>
          <AsyncButton
            icon={<i className="fa fa-download" />}
            variant="solid"
            color="cyan"
            onClick={() => handleDownloadPDF('L')}>
            Tải template PDF VN
          </AsyncButton>
          <AsyncButton
            icon={<i className="fa fa-download" />}
            variant="solid"
            color="cyan"
            onClick={() => handleDownloadPDF('EN')}>
            Tải template PDF EN
          </AsyncButton>
          <Button
            hidden={onlyUsePopulatePDF}
            icon={<i className="fa fa-print" />}
            variant="solid"
            color="cyan"
            onClick={() => {
              setViewFileOnly(false)
              setOpenDocumentStore(false)
              setFormMode(FORM_MODE.view)

              handlePrintPDF(`GiayChapThuan_${medicalRecordDetail?.title || ''}`)
            }}>
            In phiếu
          </Button>
        </div>
      </div>

      <div
        className="mt-2 mb-4 shadow-md p-3 rounded-sm"
        style={{ display: openDocumentStore ? 'block' : 'none' }}>
        <DocumentStore
          dataSource={lists.medical_record_form.listName}
          parentID={4} // 4 is DocumentStore
          storeID={selectedMedicalRecordId}
          mode={'Edit'}
          setAttachments={setAttachments}
        />
      </div>

      <div
        id="medical-record-form-print"
        hidden={viewFileOnly || onlyUsePopulatePDF}
        className="mt-3 px-4">
        <Form form={form} layout="vertical">
          <div className="mb-4">
            <div>
              <img
                src={fvLogoWithText}
                alt="FV Hospital"
                style={{ height: '40px', marginBottom: '10px' }}
                onError={(e) => {
                  e.target.style.display = 'none'
                  e.target.nextSibling.style.display = 'block'
                }}
              />
              <div style={{ display: 'none' }}>FV THOMSON</div>
            </div>
            <h4 className="text-center fw-bold">
              GIẤY CHẤP THUẬN GIỚI HẠN CHI TRẢ CỦA BẢO HIỂM XÃ HỘI TẠI BỆNH VIỆN FV
            </h4>
          </div>

          {/* Patient Information */}
          <div className="fw-bold text-primary my-2">Thông tin cá nhân</div>
          <Row gutter={24}>
            <Col span={14}>
              <div>Họ tên: {fullName}</div>
              <div>Giới tính: {sex}</div>
              <div>Số điện thoại: </div>
              <div>Địa chỉ: {firstCard?.card_address || ''}</div>
              <div>Số CCCD: {nationId}</div>
              <div>Số thẻ bảo hiểm xã hội: {firstCard?.card_code || ''}</div>
            </Col>

            <Col span={10}>
              <div>Ngày sinh: {dob}</div>
              <div>Mã số bệnh nhân (HN): {HN}</div>
            </Col>
          </Row>

          <div className="fw-bold text-primary my-2 mt-3">
            Thông tin cá nhân của người ký thay bệnh nhân
          </div>
          <Row gutter={24}>
            <Col span={14}>
              <div>Họ tên:</div>
              <div>Số CCCD:</div>
              <div>Địa chỉ:</div>
            </Col>
            <Col span={10}>
              <div>Mối quan hệ:</div>
              <div>Số điện thoại:</div>
            </Col>
          </Row>

          <div className="mt-3">
            <div dangerouslySetInnerHTML={{ __html: approvalLetterContent }}></div>

            <div className="d-flex gap-2 align-items-center mt-5">
              <MedicalRecordField
                form={form}
                formMode={formMode}
                label="Ngày ký:"
                labelBold={false}
                labelClassName="w-[70px]"
                fieldName="signedDateTime"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.DATE}
              />
            </div>
          </div>
        </Form>
      </div>

      <div hidden={!viewFileOnly} className="mt-3">
        <PdfViewer
          serverRelativeUrl={attachments[0]?.ServerRelativeUrl}
          fileName={attachments[0]?.Name}
        />
      </div>
    </div>
  )
}

export default ApprovalLetterForm
