import { useState, useCallback, useEffect } from 'react'
import { PDFDocument, PDFTextField, PDFCheckBox, PDFRadioGroup, PDFDropdown } from 'pdf-lib'
import { handleError } from '../helpers'

/**
 * Legacy hook for populating PDF forms and exporting them with custom fonts
 *
 * @param {File|Blob|string} pdfFile - PDF file object, Blob, or base64 string
 * @returns {Object} - Object containing:
 *   - fieldList: Array of PDF form fields with their properties
 *   - setFieldList: Function to update the fieldList from external components
 *   - populateAndExportPDF: Async function to populate fields and download PDF
 */
export function usePopulatePDF(pdfFile) {
  const [fieldList, setFieldList] = useState([])

  /**
   * Extract form fields from PDF file
   */
  const extractFormFields = useCallback(async (file) => {
    if (!file) {
      setFieldList([])
      return
    }

    try {
      let pdfBytes

      // Handle different input types
      if (file instanceof File || file instanceof Blob) {
        pdfBytes = await file.arrayBuffer()
      } else if (typeof file === 'string') {
        // Handle base64 string
        const base64Data = file.includes(',') ? file.split(',')[1] : file
        const binaryString = atob(base64Data)
        const bytes = new Uint8Array(binaryString.length)
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i)
        }
        pdfBytes = bytes.buffer
      } else {
        throw new Error('Unsupported file type. Expected File, Blob, or base64 string.')
      }

      const pdfDoc = await PDFDocument.load(pdfBytes)
      const form = pdfDoc.getForm()
      const fields = form.getFields()

      const extractedFields = fields.map((field) => {
        const fieldName = field.getName()
        let fieldType = 'unknown'
        let fieldValue = ''
        let options = []

        // Determine field type and get current value
        if (field instanceof PDFTextField) {
          fieldType = 'text'
          fieldValue = field.getText() || ''
        } else if (field instanceof PDFCheckBox) {
          fieldType = 'checkbox'
          fieldValue = field.isChecked()
        } else if (field instanceof PDFRadioGroup) {
          fieldType = 'radio'
          fieldValue = field.getSelected() || ''
          options = field.getOptions()
        } else if (field instanceof PDFDropdown) {
          fieldType = 'dropdown'
          fieldValue = field.getSelected() || []
          options = field.getOptions()
        }

        return {
          name: fieldName,
          type: fieldType,
          value: fieldValue,
          options: options,
        }
      })

      setFieldList(extractedFields)
    } catch (error) {
      const errorMessage = handleError(error, 'extractFormFields')
      setFieldList([])
      throw new Error(`Failed to extract PDF form fields: ${errorMessage}`)
    }
  }, [])

  /**
   * Populate PDF form fields and trigger download
   * @param {Object|Array} fieldValues - Object mapping field names to values or array of field objects
   * @param {string} fileName - Optional custom filename for download
   */
  const populateAndExportPDF = useCallback(
    async (fieldValues, fileName = 'populated-form.pdf') => {
      if (!pdfFile) {
        throw new Error('No PDF file provided')
      }

      try {
        let pdfBytes

        // Handle different input types (same logic as extractFormFields)
        if (pdfFile instanceof File || pdfFile instanceof Blob) {
          pdfBytes = await pdfFile.arrayBuffer()
        } else if (typeof pdfFile === 'string') {
          const base64Data = pdfFile.includes(',') ? pdfFile.split(',')[1] : pdfFile
          const binaryString = atob(base64Data)
          const bytes = new Uint8Array(binaryString.length)
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i)
          }
          pdfBytes = bytes.buffer
        } else {
          throw new Error('Unsupported file type. Expected File, Blob, or base64 string.')
        }

        const pdfDoc = await PDFDocument.load(pdfBytes)
        const form = pdfDoc.getForm()

        // Convert array format to object format if needed
        let fieldsToPopulate = fieldValues
        if (Array.isArray(fieldValues)) {
          fieldsToPopulate = fieldValues.reduce((acc, field) => {
            if (field.name && field.value !== undefined) {
              acc[field.name] = field.value
            }
            return acc
          }, {})
        }

        // Helper function to sanitize text for PDF compatibility
        const sanitizeTextForPDF = (text) => {
          if (!text) return ''

          const str = String(text)

          // Replace Vietnamese characters that might cause encoding issues
          const vietnameseMap = {
            Đ: 'D',
            đ: 'd',
            À: 'A',
            Á: 'A',
            Ả: 'A',
            Ã: 'A',
            Ạ: 'A',
            à: 'a',
            á: 'a',
            ả: 'a',
            ã: 'a',
            ạ: 'a',
            Ă: 'A',
            Ắ: 'A',
            Ằ: 'A',
            Ẳ: 'A',
            Ẵ: 'A',
            Ặ: 'A',
            ă: 'a',
            ắ: 'a',
            ằ: 'a',
            ẳ: 'a',
            ẵ: 'a',
            ặ: 'a',
            Â: 'A',
            Ấ: 'A',
            Ầ: 'A',
            Ẩ: 'A',
            Ẫ: 'A',
            Ậ: 'A',
            â: 'a',
            ấ: 'a',
            ầ: 'a',
            ẩ: 'a',
            ẫ: 'a',
            ậ: 'a',
            È: 'E',
            É: 'E',
            Ẻ: 'E',
            Ẽ: 'E',
            Ẹ: 'E',
            è: 'e',
            é: 'e',
            ẻ: 'e',
            ẽ: 'e',
            ẹ: 'e',
            Ê: 'E',
            Ế: 'E',
            Ề: 'E',
            Ể: 'E',
            Ễ: 'E',
            Ệ: 'E',
            ê: 'e',
            ế: 'e',
            ề: 'e',
            ể: 'e',
            ễ: 'e',
            ệ: 'e',
            Ì: 'I',
            Í: 'I',
            Ỉ: 'I',
            Ĩ: 'I',
            Ị: 'I',
            ì: 'i',
            í: 'i',
            ỉ: 'i',
            ĩ: 'i',
            ị: 'i',
            Ò: 'O',
            Ó: 'O',
            Ỏ: 'O',
            Õ: 'O',
            Ọ: 'O',
            ò: 'o',
            ó: 'o',
            ỏ: 'o',
            õ: 'o',
            ọ: 'o',
            Ô: 'O',
            Ố: 'O',
            Ồ: 'O',
            Ổ: 'O',
            Ỗ: 'O',
            Ộ: 'O',
            ô: 'o',
            ố: 'o',
            ồ: 'o',
            ổ: 'o',
            ỗ: 'o',
            ộ: 'o',
            Ơ: 'O',
            Ớ: 'O',
            Ờ: 'O',
            Ở: 'O',
            Ỡ: 'O',
            Ợ: 'O',
            ơ: 'o',
            ớ: 'o',
            ờ: 'o',
            ở: 'o',
            ỡ: 'o',
            ợ: 'o',
            Ù: 'U',
            Ú: 'U',
            Ủ: 'U',
            Ũ: 'U',
            Ụ: 'U',
            ù: 'u',
            ú: 'u',
            ủ: 'u',
            ũ: 'u',
            ụ: 'u',
            Ư: 'U',
            Ứ: 'U',
            Ừ: 'U',
            Ử: 'U',
            Ữ: 'U',
            Ự: 'U',
            ư: 'u',
            ứ: 'u',
            ừ: 'u',
            ử: 'u',
            ữ: 'u',
            ự: 'u',
            Ỳ: 'Y',
            Ý: 'Y',
            Ỷ: 'Y',
            Ỹ: 'Y',
            Ỵ: 'Y',
            ỳ: 'y',
            ý: 'y',
            ỷ: 'y',
            ỹ: 'y',
            ỵ: 'y',
          }

          let sanitized = str
          Object.entries(vietnameseMap).forEach(([vietnamese, ascii]) => {
            sanitized = sanitized.replace(new RegExp(vietnamese, 'g'), ascii)
          })

          // Remove any remaining non-ASCII characters that might cause issues
          // eslint-disable-next-line no-control-regex
          sanitized = sanitized.replace(/[^\x00-\x7F]/g, '')

          return sanitized
        }

        // Populate form fields
        Object.entries(fieldsToPopulate).forEach(([fieldName, fieldValue]) => {
          try {
            const field = form.getField(fieldName)

            if (field instanceof PDFTextField) {
              const sanitizedText = sanitizeTextForPDF(fieldValue)
              field.setText(sanitizedText)
            } else if (field instanceof PDFCheckBox) {
              if (fieldValue) {
                field.check()
              } else {
                field.uncheck()
              }
            } else if (field instanceof PDFRadioGroup) {
              if (fieldValue && field.getOptions().includes(fieldValue)) {
                field.select(fieldValue)
              }
            } else if (field instanceof PDFDropdown) {
              if (fieldValue) {
                if (Array.isArray(fieldValue)) {
                  // Multi-select dropdown
                  field.select(fieldValue.filter((val) => field.getOptions().includes(val)))
                } else if (field.getOptions().includes(fieldValue)) {
                  field.select([fieldValue])
                }
              }
            }
          } catch {
            // Field error handling - silently continue
          }
        })

        // Generate the filled PDF
        const filledPdfBytes = await pdfDoc.save()

        // Create blob and trigger download
        const blob = new Blob([filledPdfBytes], { type: 'application/pdf' })
        const url = URL.createObjectURL(blob)

        // Create download link and trigger download
        const link = document.createElement('a')
        link.href = url
        link.download = fileName.endsWith('.pdf') ? fileName : `${fileName}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // Clean up the URL object
        URL.revokeObjectURL(url)

        return true
      } catch (error) {
        const errorMessage = handleError(error, 'populateAndExportPDF')
        throw new Error(`Failed to populate and export PDF: ${errorMessage}`)
      }
    },
    [pdfFile],
  )

  // Extract form fields when pdfFile changes
  useEffect(() => {
    extractFormFields(pdfFile)
  }, [pdfFile, extractFormFields])

  return {
    fieldList,
    setFieldList,
    populateAndExportPDF,
  }
}
