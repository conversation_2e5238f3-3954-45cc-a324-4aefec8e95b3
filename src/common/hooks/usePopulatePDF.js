import { useState, useCallback, useEffect } from 'react'
import { PDFDocument, PDFTextField, PDFCheckBox, PDFRadioGroup, PDFDropdown } from 'pdf-lib'
import { handleError } from '../helpers'

/**
 * Legacy hook for populating PDF forms and exporting them with custom fonts
 *
 * @param {File|Blob|string} pdfFile - PDF file object, Blob, or base64 string
 * @returns {Object} - Object containing:
 *   - fieldList: Array of PDF form fields with their properties
 *   - setFieldList: Function to update the fieldList from external components
 *   - populateAndExportPDF: Async function to populate fields and download PDF
 */
export function usePopulatePDF(pdfFile) {
  const [fieldList, setFieldList] = useState([])

  /**
   * Extract form fields from PDF file
   */
  const extractFormFields = useCallback(async (file) => {
    if (!file) {
      setFieldList([])
      return
    }

    try {
      let pdfBytes

      // Handle different input types
      if (file instanceof File || file instanceof Blob) {
        pdfBytes = await file.arrayBuffer()
      } else if (typeof file === 'string') {
        // Handle base64 string
        const base64Data = file.includes(',') ? file.split(',')[1] : file
        const binaryString = atob(base64Data)
        const bytes = new Uint8Array(binaryString.length)
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i)
        }
        pdfBytes = bytes.buffer
      } else {
        throw new Error('Unsupported file type. Expected File, Blob, or base64 string.')
      }

      const pdfDoc = await PDFDocument.load(pdfBytes)
      const form = pdfDoc.getForm()
      const fields = form.getFields()

      const extractedFields = fields.map((field) => {
        const fieldName = field.getName()
        let fieldType = 'unknown'
        let fieldValue = ''
        let options = []

        // Determine field type and get current value
        if (field instanceof PDFTextField) {
          fieldType = 'text'
          fieldValue = field.getText() || ''
        } else if (field instanceof PDFCheckBox) {
          fieldType = 'checkbox'
          fieldValue = field.isChecked()
        } else if (field instanceof PDFRadioGroup) {
          fieldType = 'radio'
          fieldValue = field.getSelected() || ''
          options = field.getOptions()
        } else if (field instanceof PDFDropdown) {
          fieldType = 'dropdown'
          fieldValue = field.getSelected() || []
          options = field.getOptions()
        }

        return {
          name: fieldName,
          type: fieldType,
          value: fieldValue,
          options: options,
        }
      })

      setFieldList(extractedFields)
    } catch (error) {
      const errorMessage = handleError(error, 'extractFormFields')
      setFieldList([])
      throw new Error(`Failed to extract PDF form fields: ${errorMessage}`)
    }
  }, [])

  /**
   * Populate PDF form fields and trigger download
   * @param {Object|Array} fieldValues - Object mapping field names to values or array of field objects
   * @param {string} fileName - Optional custom filename for download
   */
  const populateAndExportPDF = useCallback(
    async (fieldValues, fileName = 'populated-form.pdf') => {
      if (!pdfFile) {
        throw new Error('No PDF file provided')
      }

      try {
        let pdfBytes

        // Handle different input types (same logic as extractFormFields)
        if (pdfFile instanceof File || pdfFile instanceof Blob) {
          pdfBytes = await pdfFile.arrayBuffer()
        } else if (typeof pdfFile === 'string') {
          const base64Data = pdfFile.includes(',') ? pdfFile.split(',')[1] : pdfFile
          const binaryString = atob(base64Data)
          const bytes = new Uint8Array(binaryString.length)
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i)
          }
          pdfBytes = bytes.buffer
        } else {
          throw new Error('Unsupported file type. Expected File, Blob, or base64 string.')
        }

        const pdfDoc = await PDFDocument.load(pdfBytes)
        const form = pdfDoc.getForm()

        // Convert array format to object format if needed
        let fieldsToPopulate = fieldValues
        if (Array.isArray(fieldValues)) {
          fieldsToPopulate = fieldValues.reduce((acc, field) => {
            if (field.name && field.value !== undefined) {
              acc[field.name] = field.value
            }
            return acc
          }, {})
        }

        // Load custom font for Vietnamese character support
        let customFont = null
        try {
          const fontResponse = await fetch('/webfonts/FV_DIN_Light.otf')
          if (fontResponse.ok) {
            const fontBytes = await fontResponse.arrayBuffer()
            customFont = await pdfDoc.embedFont(fontBytes)
          }
        } catch {
          // Fallback to default font if custom font fails to load
        }

        // Populate form fields
        Object.entries(fieldsToPopulate).forEach(([fieldName, fieldValue]) => {
          try {
            const field = form.getField(fieldName)

            if (field instanceof PDFTextField) {
              // Use Vietnamese text directly with custom font
              const textValue = String(fieldValue || '')
              field.setText(textValue)

              // Apply custom font if available
              if (customFont) {
                try {
                  field.updateAppearances(customFont)
                } catch {
                  // If custom font fails, field will use default font
                }
              }
            } else if (field instanceof PDFCheckBox) {
              if (fieldValue) {
                field.check()
              } else {
                field.uncheck()
              }
            } else if (field instanceof PDFRadioGroup) {
              if (fieldValue && field.getOptions().includes(fieldValue)) {
                field.select(fieldValue)
              }
            } else if (field instanceof PDFDropdown) {
              if (fieldValue) {
                if (Array.isArray(fieldValue)) {
                  // Multi-select dropdown
                  field.select(fieldValue.filter((val) => field.getOptions().includes(val)))
                } else if (field.getOptions().includes(fieldValue)) {
                  field.select([fieldValue])
                }
              }
            }
          } catch {
            // Fallback: try with basic text only
            try {
              const field = form.getField(fieldName)
              if (field instanceof PDFTextField) {
                // Remove diacritics as last resort
                const basicText = String(fieldValue || '')
                  .normalize('NFD')
                  .replace(/[\u0300-\u036f]/g, '')
                field.setText(basicText)
              }
            } catch {
              // Silent fallback failure - field will remain empty
            }
          }
        })

        // Generate the filled PDF
        const filledPdfBytes = await pdfDoc.save()

        // Create blob and trigger download
        const blob = new Blob([filledPdfBytes], { type: 'application/pdf' })
        const url = URL.createObjectURL(blob)

        // Create download link and trigger download
        const link = document.createElement('a')
        link.href = url
        link.download = fileName.endsWith('.pdf') ? fileName : `${fileName}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // Clean up the URL object
        URL.revokeObjectURL(url)

        return true
      } catch (error) {
        const errorMessage = handleError(error, 'populateAndExportPDF')
        throw new Error(`Failed to populate and export PDF: ${errorMessage}`)
      }
    },
    [pdfFile],
  )

  // Extract form fields when pdfFile changes
  useEffect(() => {
    extractFormFields(pdfFile)
  }, [pdfFile, extractFormFields])

  return {
    fieldList,
    setFieldList,
    populateAndExportPDF,
  }
}
