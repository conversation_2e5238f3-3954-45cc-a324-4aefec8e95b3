import React, { useState, useCallback } from 'react'
import { PDFDocument, PDFTextField, PDFCheckBox, PDFRadioGroup, PDFDropdown } from 'pdf-lib'
import { Card, Form, Input, Checkbox, Radio, Select, Space, Row, Col, App } from 'antd'
import { handleError } from '../helpers'
import { uploadFileToDocLibService } from '../services'
import PdfViewer from './PdfViewer'
import Async<PERSON>utton from './AsyncButton'
import { v4 } from 'uuid'

/**
 * Dynamic PDF Form Filling Component
 *
 * @param {Object} props - Component props
 * @param {Array} props.templates - Array of PDF template configurations
 * @param {string} props.storeID - Store ID for DocumentStore
 * @param {string} props.dataSource - Data source for DocumentStore
 * @param {Function} props.onSaveSuccess - Callback when PDF is saved successfully
 * @param {Object} props.defaultValues - Default values for form fields
 * @param {Object} props.savedFieldValues - Previously saved field values from note column
 */
const PopulatePDF = ({
  templates = [],
  storeID,
  dataSource,
  onSaveSuccess,
  defaultValues = {},
  savedFieldValues = {},
}) => {
  const { message } = App.useApp()
  const [form] = Form.useForm()

  // State management
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [pdfFile, setPdfFile] = useState(null)
  const [fieldList, setFieldList] = useState([])
  const [previewPdf, setPreviewPdf] = useState(null)
  const [loading, setLoading] = useState(false)

  /**
   * Extract form fields from PDF file
   */
  const extractFormFields = useCallback(async (file) => {
    if (!file) {
      setFieldList([])
      return
    }

    try {
      let pdfBytes

      // Handle different input types
      if (file instanceof File || file instanceof Blob) {
        pdfBytes = await file.arrayBuffer()
      } else if (typeof file === 'string') {
        // Handle base64 string
        const base64Data = file.includes(',') ? file.split(',')[1] : file
        const binaryString = atob(base64Data)
        const bytes = new Uint8Array(binaryString.length)
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i)
        }
        pdfBytes = bytes.buffer
      } else {
        throw new Error('Unsupported file type. Expected File, Blob, or base64 string.')
      }

      const pdfDoc = await PDFDocument.load(pdfBytes)
      const form = pdfDoc.getForm()
      const fields = form.getFields()

      const extractedFields = fields.map((field) => {
        const fieldName = field.getName()
        let fieldType = 'unknown'
        let fieldValue = ''
        let options = []

        // Determine field type and get current value
        if (field instanceof PDFTextField) {
          fieldType = 'text'
          fieldValue = field.getText() || ''
        } else if (field instanceof PDFCheckBox) {
          fieldType = 'checkbox'
          fieldValue = field.isChecked()
        } else if (field instanceof PDFRadioGroup) {
          fieldType = 'radio'
          fieldValue = field.getSelected() || ''
          options = field.getOptions()
        } else if (field instanceof PDFDropdown) {
          fieldType = 'dropdown'
          fieldValue = field.getSelected() || []
          options = field.getOptions()
        }

        return {
          name: fieldName,
          type: fieldType,
          value: fieldValue,
          options: options,
        }
      })

      setFieldList(extractedFields)
    } catch (error) {
      const errorMessage = handleError(error, 'extractFormFields')
      setFieldList([])
      throw new Error(`Failed to extract PDF form fields: ${errorMessage}`)
    }
  }, [])

  /**
   * Populate form with default and saved values when fields are extracted
   */
  useEffect(() => {
    if (fieldList.length > 0) {
      const formValues = {}
      fieldList.forEach((field) => {
        const savedValue = savedFieldValues[field.name]
        const defaultValue = defaultValues[field.name]
        formValues[field.name] = savedValue !== undefined ? savedValue : defaultValue || field.value
      })
      form.setFieldsValue(formValues)
    }
  }, [fieldList, defaultValues, savedFieldValues, form])

  /**
   * Load PDF template from URL
   */
  const loadTemplate = useCallback(
    async (templateUrl) => {
      try {
        setLoading(true)
        const response = await fetch(templateUrl)
        if (!response.ok) {
          throw new Error('Failed to fetch PDF template')
        }
        const blob = await response.blob()
        setPdfFile(blob)
        await extractFormFields(blob)
      } catch (error) {
        handleError(error, 'loadTemplate')
        message.error('Không thể tải template PDF')
      } finally {
        setLoading(false)
      }
    },
    [extractFormFields, message],
  )

  /**
   * Handle template selection
   */
  const handleTemplateChange = (templateId) => {
    const template = templates.find((t) => t.id === templateId)
    if (template) {
      setSelectedTemplate(template)
      loadTemplate(template.url)
    }
  }

  /**
   * Generate preview PDF with current form values
   */
  const handlePreview = useCallback(async () => {
    if (!pdfFile) {
      message.error('Chưa có template PDF')
      return
    }

    try {
      setLoading(true)
      const formValues = form.getFieldsValue()
      const filledPdf = await populatePDF(pdfFile, formValues)
      setPreviewPdf(filledPdf)
      message.success('Tạo preview thành công')
    } catch (error) {
      handleError(error, 'handlePreview')
      message.error('Không thể tạo preview PDF')
    } finally {
      setLoading(false)
    }
  }, [pdfFile, form, message])

  /**
   * Save filled PDF to DocumentStore
   */
  const handleSaveAttachment = useCallback(async () => {
    if (!pdfFile || !storeID || !dataSource) {
      message.error('Thiếu thông tin cần thiết để lưu file')
      return
    }

    try {
      setLoading(true)
      const formValues = form.getFieldsValue()

      // Generate filled PDF
      const filledPdfBytes = await populatePDF(pdfFile, formValues)
      const fileName = `${selectedTemplate?.name || 'filled-form'}_${new Date().getTime()}.pdf`

      // Create file blob
      const file = new Blob([filledPdfBytes], { type: 'application/pdf' })

      // Upload to DocumentStore
      const uniqueFileName = `${v4()}_${fileName}`
      await uploadFileToDocLibService('/DocumentStore', uniqueFileName, storeID, dataSource, file)

      // Save field values to note column (will be handled by parent component)
      if (onSaveSuccess) {
        onSaveSuccess({
          fieldValues: formValues,
          templateId: selectedTemplate?.id,
          fileName: uniqueFileName,
        })
      }

      message.success('Lưu file thành công')
    } catch (error) {
      handleError(error, 'handleSaveAttachment')
      message.error('Không thể lưu file')
    } finally {
      setLoading(false)
    }
  }, [pdfFile, storeID, dataSource, form, selectedTemplate, onSaveSuccess, message])

  /**
   * Core PDF population function
   */
  const populatePDF = useCallback(async (file, fieldValues) => {
    let pdfBytes

    // Handle different input types
    if (file instanceof File || file instanceof Blob) {
      pdfBytes = await file.arrayBuffer()
    } else if (typeof file === 'string') {
      const base64Data = file.includes(',') ? file.split(',')[1] : file
      const binaryString = atob(base64Data)
      const bytes = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }
      pdfBytes = bytes.buffer
    } else {
      throw new Error('Unsupported file type. Expected File, Blob, or base64 string.')
    }

    const pdfDoc = await PDFDocument.load(pdfBytes)
    const form = pdfDoc.getForm()

    // Populate form fields
    Object.entries(fieldValues).forEach(([fieldName, fieldValue]) => {
      try {
        const field = form.getField(fieldName)

        if (field instanceof PDFTextField) {
          field.setText(String(fieldValue || ''))
        } else if (field instanceof PDFCheckBox) {
          if (fieldValue) {
            field.check()
          } else {
            field.uncheck()
          }
        } else if (field instanceof PDFRadioGroup) {
          if (fieldValue && field.getOptions().includes(fieldValue)) {
            field.select(fieldValue)
          }
        } else if (field instanceof PDFDropdown) {
          if (fieldValue) {
            if (Array.isArray(fieldValue)) {
              field.select(fieldValue.filter((val) => field.getOptions().includes(val)))
            } else if (field.getOptions().includes(fieldValue)) {
              field.select([fieldValue])
            }
          }
        }
      } catch (fieldError) {
        // Field error handling - silently continue
      }
    })

    return await pdfDoc.save()
  }, [])

  /**
   * Render form field based on type
   */
  const renderFormField = (field) => {
    const { name, type, options } = field

    switch (type) {
      case 'text':
        return (
          <Form.Item key={name} name={name} label={name}>
            <Input placeholder={`Nhập ${name}`} />
          </Form.Item>
        )

      case 'checkbox':
        return (
          <Form.Item key={name} name={name} valuePropName="checked">
            <Checkbox>{name}</Checkbox>
          </Form.Item>
        )

      case 'radio':
        return (
          <Form.Item key={name} name={name} label={name}>
            <Radio.Group>
              {options.map((option) => (
                <Radio key={option} value={option}>
                  {option}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        )

      case 'dropdown':
        return (
          <Form.Item key={name} name={name} label={name}>
            <Select placeholder={`Chọn ${name}`} allowClear>
              {options.map((option) => (
                <Select.Option key={option} value={option}>
                  {option}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )

      default:
        return (
          <Form.Item key={name} name={name} label={name}>
            <Input placeholder={`Nhập ${name}`} />
          </Form.Item>
        )
    }
  }

  // If no templates configured, return null (fallback to upload functionality)
  if (!templates || templates.length === 0) {
    return null
  }

  return (
    <div>
      <Card title="Điền thông tin PDF Template" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* Template Selection */}
          <Form.Item label="Chọn Template">
            <Select
              placeholder="Chọn template PDF"
              onChange={handleTemplateChange}
              value={selectedTemplate?.id}
              style={{ width: '100%' }}>
              {templates.map((template) => (
                <Select.Option key={template.id} value={template.id}>
                  {template.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* Action Buttons */}
          {selectedTemplate && (
            <Space>
              <AsyncButton
                type="primary"
                onClick={handlePreview}
                loading={loading}
                icon={<i className="fa fa-eye" />}>
                Preview
              </AsyncButton>
              <AsyncButton
                type="primary"
                onClick={handleSaveAttachment}
                loading={loading}
                icon={<i className="fa fa-save" />}>
                Lưu vào DocumentStore
              </AsyncButton>
            </Space>
          )}
        </Space>
      </Card>

      {/* Form Fields */}
      {fieldList.length > 0 && (
        <Card title="Thông tin điền vào PDF" style={{ marginBottom: 16 }}>
          <Form form={form} layout="vertical">
            <Row gutter={[16, 16]}>
              {fieldList.map((field) => (
                <Col key={field.name} xs={24} sm={12} md={8}>
                  {renderFormField(field)}
                </Col>
              ))}
            </Row>
          </Form>
        </Card>
      )}

      {/* PDF Preview */}
      {previewPdf && (
        <Card title="Preview PDF">
          <PdfViewer fileContent={previewPdf} />
        </Card>
      )}
    </div>
  )
}

export default PopulatePDF
